/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.android.systemui.R;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Controller for the innovative Jarvis home page with modern UI design.
 * 
 * Features:
 * - Animated particle background
 * - Dynamic AI status indicators
 * - Contextual quick actions
 * - AI insights widget
 * - Smooth animations and transitions
 */
public class JarvisHomePageController {
    private static final String TAG = "JarvisHomePageController";
    private static final boolean DEBUG = true;
    
    private static final int PARTICLE_COUNT = 20;
    private static final int ANIMATION_DURATION = 300;
    private static final int PARTICLE_ANIMATION_DURATION = 10000;
    
    private final Context mContext;
    private final Handler mMainHandler;
    
    // UI Components
    private ViewGroup mHomePageContainer;
    private View mParticleSystem;
    private ImageView mAiStatusIndicator;
    private TextView mAiStatusText;
    private TextView mWelcomeMessage;
    private ViewGroup mQuickActionsGrid;
    private RecyclerView mInsightsRecyclerView;
    private RecyclerView mRecentActivityRecyclerView;
    
    // Quick Action Cards
    private View mVoiceAssistantCard;
    private View mSmartControlsCard;
    private View mAiSuggestionsCard;
    private View mSettingsCard;
    
    // Animation and Effects
    private List<Particle> mParticles;
    private ValueAnimator mParticleAnimator;
    private ObjectAnimator mStatusPulseAnimator;
    
    // Data
    private List<AiInsight> mCurrentInsights;
    private List<RecentActivity> mRecentActivities;
    
    // Listeners
    private HomePageListener mListener;
    
    public interface HomePageListener {
        void onQuickActionClicked(String actionId);
        void onVoiceActivationRequested();
        void onInsightClicked(AiInsight insight);
        void onRecentActivityClicked(RecentActivity activity);
    }
    
    public JarvisHomePageController(Context context) {
        mContext = context;
        mMainHandler = new Handler(Looper.getMainLooper());
        
        mParticles = new ArrayList<>();
        mCurrentInsights = new ArrayList<>();
        mRecentActivities = new ArrayList<>();
        
        initializeParticles();
        
        if (DEBUG) Log.d(TAG, "JarvisHomePageController initialized");
    }
    
    public void setHomePageListener(HomePageListener listener) {
        mListener = listener;
    }
    
    public View createHomePageView(ViewGroup parent) {
        LayoutInflater inflater = LayoutInflater.from(mContext);
        mHomePageContainer = (ViewGroup) inflater.inflate(R.layout.jarvis_home_page, parent, false);
        
        initializeViews();
        setupClickHandlers();
        setupAnimations();
        
        return mHomePageContainer;
    }
    
    private void initializeViews() {
        // Particle system
        mParticleSystem = mHomePageContainer.findViewById(R.id.particle_system);
        
        // Header components
        mAiStatusIndicator = mHomePageContainer.findViewById(R.id.ai_status_indicator);
        mAiStatusText = mHomePageContainer.findViewById(R.id.ai_status_text);
        mWelcomeMessage = mHomePageContainer.findViewById(R.id.welcome_message);
        
        // Quick actions
        mQuickActionsGrid = mHomePageContainer.findViewById(R.id.quick_actions_grid);
        mVoiceAssistantCard = mHomePageContainer.findViewById(R.id.voice_assistant_card);
        mSmartControlsCard = mHomePageContainer.findViewById(R.id.smart_controls_card);
        mAiSuggestionsCard = mHomePageContainer.findViewById(R.id.ai_suggestions_card);
        mSettingsCard = mHomePageContainer.findViewById(R.id.settings_card);
        
        // RecyclerViews
        mInsightsRecyclerView = mHomePageContainer.findViewById(R.id.insights_recycler_view);
        mRecentActivityRecyclerView = mHomePageContainer.findViewById(R.id.recent_activity_recycler_view);
        
        setupRecyclerViews();
    }
    
    private void setupRecyclerViews() {
        // Setup insights RecyclerView
        mInsightsRecyclerView.setLayoutManager(new LinearLayoutManager(mContext));
        mInsightsRecyclerView.setAdapter(new InsightsAdapter());
        
        // Setup recent activity RecyclerView
        mRecentActivityRecyclerView.setLayoutManager(new LinearLayoutManager(mContext));
        mRecentActivityRecyclerView.setAdapter(new RecentActivityAdapter());
    }
    
    private void setupClickHandlers() {
        // Quick action cards
        mVoiceAssistantCard.setOnClickListener(v -> {
            animateCardPress(v);
            if (mListener != null) {
                mListener.onQuickActionClicked("voice_assistant");
            }
        });
        
        mSmartControlsCard.setOnClickListener(v -> {
            animateCardPress(v);
            if (mListener != null) {
                mListener.onQuickActionClicked("smart_controls");
            }
        });
        
        mAiSuggestionsCard.setOnClickListener(v -> {
            animateCardPress(v);
            if (mListener != null) {
                mListener.onQuickActionClicked("ai_suggestions");
            }
        });
        
        mSettingsCard.setOnClickListener(v -> {
            animateCardPress(v);
            if (mListener != null) {
                mListener.onQuickActionClicked("settings");
            }
        });
        
        // Voice FAB
        View voiceFab = mHomePageContainer.findViewById(R.id.voice_fab);
        voiceFab.setOnClickListener(v -> {
            if (mListener != null) {
                mListener.onVoiceActivationRequested();
            }
        });
    }
    
    private void setupAnimations() {
        // Setup particle animation
        setupParticleAnimation();
        
        // Setup status indicator pulse
        setupStatusPulseAnimation();
        
        // Animate initial appearance
        animateInitialAppearance();
    }
    
    private void initializeParticles() {
        Random random = new Random();
        for (int i = 0; i < PARTICLE_COUNT; i++) {
            Particle particle = new Particle();
            particle.x = random.nextFloat();
            particle.y = random.nextFloat();
            particle.vx = (random.nextFloat() - 0.5f) * 0.02f;
            particle.vy = (random.nextFloat() - 0.5f) * 0.02f;
            particle.size = random.nextFloat() * 4 + 2;
            particle.alpha = random.nextFloat() * 0.5f + 0.1f;
            mParticles.add(particle);
        }
    }
    
    private void setupParticleAnimation() {
        if (mParticleSystem == null) return;
        
        mParticleSystem.setBackground(new ParticleDrawable());
        
        mParticleAnimator = ValueAnimator.ofFloat(0f, 1f);
        mParticleAnimator.setDuration(PARTICLE_ANIMATION_DURATION);
        mParticleAnimator.setRepeatCount(ValueAnimator.INFINITE);
        mParticleAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
        mParticleAnimator.addUpdateListener(animation -> {
            updateParticles();
            mParticleSystem.invalidate();
        });
        mParticleAnimator.start();
    }
    
    private void updateParticles() {
        for (Particle particle : mParticles) {
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // Wrap around edges
            if (particle.x < 0) particle.x = 1;
            if (particle.x > 1) particle.x = 0;
            if (particle.y < 0) particle.y = 1;
            if (particle.y > 1) particle.y = 0;
        }
    }
    
    private void setupStatusPulseAnimation() {
        if (mAiStatusIndicator == null) return;
        
        mStatusPulseAnimator = ObjectAnimator.ofFloat(mAiStatusIndicator, "alpha", 0.5f, 1.0f);
        mStatusPulseAnimator.setDuration(1500);
        mStatusPulseAnimator.setRepeatCount(ValueAnimator.INFINITE);
        mStatusPulseAnimator.setRepeatMode(ValueAnimator.REVERSE);
        mStatusPulseAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
        mStatusPulseAnimator.start();
    }
    
    private void animateInitialAppearance() {
        if (mHomePageContainer == null) return;
        
        // Animate quick action cards with staggered delay
        View[] cards = {mVoiceAssistantCard, mSmartControlsCard, mAiSuggestionsCard, mSettingsCard};
        for (int i = 0; i < cards.length; i++) {
            if (cards[i] != null) {
                cards[i].setAlpha(0f);
                cards[i].setTranslationY(50f);
                cards[i].animate()
                    .alpha(1f)
                    .translationY(0f)
                    .setDuration(ANIMATION_DURATION)
                    .setStartDelay(i * 100)
                    .setInterpolator(new AccelerateDecelerateInterpolator())
                    .start();
            }
        }
    }
    
    private void animateCardPress(View card) {
        card.animate()
            .scaleX(0.95f)
            .scaleY(0.95f)
            .setDuration(100)
            .setListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    card.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(100)
                        .setListener(null)
                        .start();
                }
            })
            .start();
    }
    
    public void updateAiStatus(String status, boolean isActive) {
        if (mAiStatusText != null) {
            mAiStatusText.setText(status);
        }
        
        if (mAiStatusIndicator != null) {
            int colorRes = isActive ? R.color.jarvis_ai_active : R.color.jarvis_ai_error;
            mAiStatusIndicator.setColorFilter(mContext.getColor(colorRes));
        }
    }
    
    public void updateInsights(List<AiInsight> insights) {
        mCurrentInsights.clear();
        if (insights != null) {
            mCurrentInsights.addAll(insights);
        }
        
        if (mInsightsRecyclerView != null && mInsightsRecyclerView.getAdapter() != null) {
            mInsightsRecyclerView.getAdapter().notifyDataSetChanged();
        }
    }
    
    public void updateRecentActivity(List<RecentActivity> activities) {
        mRecentActivities.clear();
        if (activities != null) {
            mRecentActivities.addAll(activities);
        }
        
        if (mRecentActivityRecyclerView != null && mRecentActivityRecyclerView.getAdapter() != null) {
            mRecentActivityRecyclerView.getAdapter().notifyDataSetChanged();
        }
    }
    
    public void destroy() {
        if (mParticleAnimator != null && mParticleAnimator.isRunning()) {
            mParticleAnimator.cancel();
        }
        
        if (mStatusPulseAnimator != null && mStatusPulseAnimator.isRunning()) {
            mStatusPulseAnimator.cancel();
        }
        
        if (DEBUG) Log.d(TAG, "JarvisHomePageController destroyed");
    }
    
    // Particle class for animated background
    private static class Particle {
        float x, y;
        float vx, vy;
        float size;
        float alpha;
    }

    // Custom drawable for particle system
    private class ParticleDrawable extends android.graphics.drawable.Drawable {
        private Paint mPaint;

        public ParticleDrawable() {
            mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            mPaint.setColor(mContext.getColor(R.color.jarvis_primary));
        }

        @Override
        public void draw(Canvas canvas) {
            int width = getBounds().width();
            int height = getBounds().height();

            for (Particle particle : mParticles) {
                mPaint.setAlpha((int) (particle.alpha * 255));
                canvas.drawCircle(
                    particle.x * width,
                    particle.y * height,
                    particle.size,
                    mPaint
                );
            }
        }

        @Override
        public void setAlpha(int alpha) {}

        @Override
        public void setColorFilter(android.graphics.ColorFilter colorFilter) {}

        @Override
        public int getOpacity() {
            return android.graphics.PixelFormat.TRANSLUCENT;
        }
    }

    // Data models
    public static class AiInsight {
        public String title;
        public String description;
        public String iconResource;
        public String actionId;
    }

    public static class RecentActivity {
        public String title;
        public String subtitle;
        public String timestamp;
        public String iconResource;
        public String activityId;
    }

    // RecyclerView Adapters
    private class InsightsAdapter extends RecyclerView.Adapter<InsightsAdapter.ViewHolder> {
        @Override
        public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(mContext).inflate(
                R.layout.jarvis_insight_item, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(ViewHolder holder, int position) {
            AiInsight insight = mCurrentInsights.get(position);
            holder.bind(insight);
        }

        @Override
        public int getItemCount() {
            return mCurrentInsights.size();
        }

        class ViewHolder extends RecyclerView.ViewHolder {
            TextView title, description;
            ImageView icon;

            ViewHolder(View itemView) {
                super(itemView);
                title = itemView.findViewById(R.id.insight_title);
                description = itemView.findViewById(R.id.insight_description);
                icon = itemView.findViewById(R.id.insight_icon);

                itemView.setOnClickListener(v -> {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION && mListener != null) {
                        mListener.onInsightClicked(mCurrentInsights.get(position));
                    }
                });
            }

            void bind(AiInsight insight) {
                title.setText(insight.title);
                description.setText(insight.description);
                // Set icon based on insight.iconResource
            }
        }
    }

    private class RecentActivityAdapter extends RecyclerView.Adapter<RecentActivityAdapter.ViewHolder> {
        @Override
        public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(mContext).inflate(
                R.layout.jarvis_recent_activity_item, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(ViewHolder holder, int position) {
            RecentActivity activity = mRecentActivities.get(position);
            holder.bind(activity);
        }

        @Override
        public int getItemCount() {
            return mRecentActivities.size();
        }

        class ViewHolder extends RecyclerView.ViewHolder {
            TextView title, subtitle, timestamp;
            ImageView icon;

            ViewHolder(View itemView) {
                super(itemView);
                title = itemView.findViewById(R.id.activity_title);
                subtitle = itemView.findViewById(R.id.activity_subtitle);
                timestamp = itemView.findViewById(R.id.activity_timestamp);
                icon = itemView.findViewById(R.id.activity_icon);

                itemView.setOnClickListener(v -> {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION && mListener != null) {
                        mListener.onRecentActivityClicked(mRecentActivities.get(position));
                    }
                });
            }

            void bind(RecentActivity activity) {
                title.setText(activity.title);
                subtitle.setText(activity.subtitle);
                timestamp.setText(activity.timestamp);
                // Set icon based on activity.iconResource
            }
        }
    }
}
