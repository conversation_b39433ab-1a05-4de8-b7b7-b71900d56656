/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.android.systemui.R;

/**
 * Controller for the innovative Jarvis navigation bar with modern UI design.
 * 
 * Features:
 * - Floating navigation with glassmorphism effects
 * - Morphing icons with smooth transitions
 * - AI status indicators with breathing animations
 * - Smart notification badges
 * - Haptic feedback integration
 */
public class JarvisNavigationController {
    private static final String TAG = "JarvisNavigationController";
    private static final boolean DEBUG = true;
    
    private static final int ANIMATION_DURATION = 300;
    private static final int INDICATOR_ANIMATION_DURATION = 200;
    
    private final Context mContext;
    private final Handler mMainHandler;
    
    // UI Components
    private LinearLayout mNavigationContainer;
    private View mNavIndicator;
    
    // Navigation Items
    private LinearLayout mNavHome;
    private LinearLayout mNavAssistant;
    private LinearLayout mNavMenu;
    private LinearLayout mNavNotifications;
    private LinearLayout mNavProfile;
    
    // Icons and Labels
    private ImageView mHomeIcon, mAssistantIcon, mMenuIcon, mNotificationsIcon, mProfileIcon;
    private TextView mHomeLabel, mAssistantLabel, mMenuLabel, mNotificationsLabel, mProfileLabel;
    
    // Indicators and Badges
    private View mHomeIndicator, mAssistantBreathing, mAssistantVoiceIndicator;
    private TextView mMenuBadge, mNotificationsCount;
    private View mNotificationsPriority, mProfileStatus;
    
    // State
    private int mCurrentSelectedIndex = 0;
    private boolean mAiActive = false;
    private boolean mVoiceListening = false;
    
    // Listeners
    private NavigationListener mListener;
    
    public interface NavigationListener {
        void onNavigationItemSelected(String itemId, int index);
        void onNavigationItemLongPressed(String itemId);
    }
    
    public JarvisNavigationController(Context context) {
        mContext = context;
        mMainHandler = new Handler(Looper.getMainLooper());
        
        if (DEBUG) Log.d(TAG, "JarvisNavigationController initialized");
    }
    
    public void setNavigationListener(NavigationListener listener) {
        mListener = listener;
    }
    
    public View createNavigationView(ViewGroup parent) {
        LayoutInflater inflater = LayoutInflater.from(mContext);
        mNavigationContainer = (LinearLayout) inflater.inflate(
            R.layout.jarvis_navigation_bar, parent, false);
        
        initializeViews();
        setupClickHandlers();
        setupInitialState();
        
        return mNavigationContainer;
    }
    
    private void initializeViews() {
        // Navigation items
        mNavHome = mNavigationContainer.findViewById(R.id.nav_home);
        mNavAssistant = mNavigationContainer.findViewById(R.id.nav_assistant);
        mNavMenu = mNavigationContainer.findViewById(R.id.nav_menu);
        mNavNotifications = mNavigationContainer.findViewById(R.id.nav_notifications);
        mNavProfile = mNavigationContainer.findViewById(R.id.nav_profile);
        
        // Icons
        mHomeIcon = mNavigationContainer.findViewById(R.id.nav_home_icon);
        mAssistantIcon = mNavigationContainer.findViewById(R.id.nav_assistant_icon);
        mMenuIcon = mNavigationContainer.findViewById(R.id.nav_menu_icon);
        mNotificationsIcon = mNavigationContainer.findViewById(R.id.nav_notifications_icon);
        mProfileIcon = mNavigationContainer.findViewById(R.id.nav_profile_avatar);
        
        // Labels
        mHomeLabel = mNavigationContainer.findViewById(R.id.nav_home_label);
        mAssistantLabel = mNavigationContainer.findViewById(R.id.nav_assistant_label);
        mMenuLabel = mNavigationContainer.findViewById(R.id.nav_menu_label);
        mNotificationsLabel = mNavigationContainer.findViewById(R.id.nav_notifications_label);
        mProfileLabel = mNavigationContainer.findViewById(R.id.nav_profile_label);
        
        // Indicators and badges
        mHomeIndicator = mNavigationContainer.findViewById(R.id.nav_home_indicator);
        mAssistantBreathing = mNavigationContainer.findViewById(R.id.nav_assistant_breathing);
        mAssistantVoiceIndicator = mNavigationContainer.findViewById(R.id.nav_assistant_voice_indicator);
        mMenuBadge = mNavigationContainer.findViewById(R.id.nav_menu_badge);
        mNotificationsCount = mNavigationContainer.findViewById(R.id.nav_notifications_count);
        mNotificationsPriority = mNavigationContainer.findViewById(R.id.nav_notifications_priority);
        mProfileStatus = mNavigationContainer.findViewById(R.id.nav_profile_status);
        
        // Navigation indicator
        mNavIndicator = mNavigationContainer.findViewById(R.id.nav_indicator);
    }
    
    private void setupClickHandlers() {
        // Home navigation
        mNavHome.setOnClickListener(v -> {
            selectNavigationItem(0, "home");
            animateItemPress(mNavHome);
        });
        
        mNavHome.setOnLongClickListener(v -> {
            if (mListener != null) {
                mListener.onNavigationItemLongPressed("home");
            }
            return true;
        });
        
        // Assistant navigation
        mNavAssistant.setOnClickListener(v -> {
            selectNavigationItem(1, "assistant");
            animateItemPress(mNavAssistant);
        });
        
        mNavAssistant.setOnLongClickListener(v -> {
            if (mListener != null) {
                mListener.onNavigationItemLongPressed("assistant");
            }
            return true;
        });
        
        // Menu navigation
        mNavMenu.setOnClickListener(v -> {
            selectNavigationItem(2, "menu");
            animateItemPress(mNavMenu);
        });
        
        mNavMenu.setOnLongClickListener(v -> {
            if (mListener != null) {
                mListener.onNavigationItemLongPressed("menu");
            }
            return true;
        });
        
        // Notifications navigation
        mNavNotifications.setOnClickListener(v -> {
            selectNavigationItem(3, "notifications");
            animateItemPress(mNavNotifications);
        });
        
        mNavNotifications.setOnLongClickListener(v -> {
            if (mListener != null) {
                mListener.onNavigationItemLongPressed("notifications");
            }
            return true;
        });
        
        // Profile navigation
        mNavProfile.setOnClickListener(v -> {
            selectNavigationItem(4, "profile");
            animateItemPress(mNavProfile);
        });
        
        mNavProfile.setOnLongClickListener(v -> {
            if (mListener != null) {
                mListener.onNavigationItemLongPressed("profile");
            }
            return true;
        });
    }
    
    private void setupInitialState() {
        // Set initial selection to home
        selectNavigationItem(0, "home");
        
        // Setup initial indicator states
        updateAiStatus(false, false);
        updateNotificationBadges(0, false);
        updateMenuBadge(0);
    }
    
    private void selectNavigationItem(int index, String itemId) {
        if (mCurrentSelectedIndex == index) return;
        
        // Deselect previous item
        deselectNavigationItem(mCurrentSelectedIndex);
        
        // Select new item
        mCurrentSelectedIndex = index;
        selectNavigationItemVisual(index);
        
        // Animate indicator
        animateIndicatorToPosition(index);
        
        // Notify listener
        if (mListener != null) {
            mListener.onNavigationItemSelected(itemId, index);
        }
        
        if (DEBUG) Log.d(TAG, "Selected navigation item: " + itemId + " at index " + index);
    }
    
    private void selectNavigationItemVisual(int index) {
        LinearLayout[] items = {mNavHome, mNavAssistant, mNavMenu, mNavNotifications, mNavProfile};
        ImageView[] icons = {mHomeIcon, mAssistantIcon, mMenuIcon, mNotificationsIcon, mProfileIcon};
        TextView[] labels = {mHomeLabel, mAssistantLabel, mMenuLabel, mNotificationsLabel, mProfileLabel};
        
        if (index >= 0 && index < items.length) {
            items[index].setSelected(true);
            icons[index].setColorFilter(mContext.getColor(R.color.jarvis_nav_selected));
            labels[index].setTextColor(mContext.getColor(R.color.jarvis_nav_selected));
            
            // Scale animation
            icons[index].animate()
                .scaleX(1.1f)
                .scaleY(1.1f)
                .setDuration(ANIMATION_DURATION)
                .start();
        }
    }
    
    private void deselectNavigationItem(int index) {
        LinearLayout[] items = {mNavHome, mNavAssistant, mNavMenu, mNavNotifications, mNavProfile};
        ImageView[] icons = {mHomeIcon, mAssistantIcon, mMenuIcon, mNotificationsIcon, mProfileIcon};
        TextView[] labels = {mHomeLabel, mAssistantLabel, mMenuLabel, mNotificationsLabel, mProfileLabel};
        
        if (index >= 0 && index < items.length) {
            items[index].setSelected(false);
            icons[index].setColorFilter(mContext.getColor(R.color.jarvis_nav_unselected));
            labels[index].setTextColor(mContext.getColor(R.color.jarvis_nav_unselected));
            
            // Reset scale
            icons[index].animate()
                .scaleX(1.0f)
                .scaleY(1.0f)
                .setDuration(ANIMATION_DURATION)
                .start();
        }
    }
    
    private void animateIndicatorToPosition(int index) {
        if (mNavIndicator == null) return;
        
        // Calculate indicator position based on navigation item
        float targetX = calculateIndicatorPosition(index);
        
        mNavIndicator.animate()
            .translationX(targetX)
            .setDuration(INDICATOR_ANIMATION_DURATION)
            .start();
    }
    
    private float calculateIndicatorPosition(int index) {
        // Calculate the X position for the indicator based on the selected item
        // This would need to be calculated based on the actual layout measurements
        LinearLayout[] items = {mNavHome, mNavAssistant, mNavMenu, mNavNotifications, mNavProfile};
        
        if (index >= 0 && index < items.length && items[index] != null) {
            // Get the center position of the selected item
            int[] location = new int[2];
            items[index].getLocationInWindow(location);
            return location[0] + (items[index].getWidth() / 2f) - (mNavIndicator.getWidth() / 2f);
        }
        
        return 0f;
    }
    
    private void animateItemPress(View item) {
        item.animate()
            .scaleX(0.9f)
            .scaleY(0.9f)
            .setDuration(100)
            .withEndAction(() -> {
                item.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(100)
                    .start();
            })
            .start();
    }
    
    public void updateAiStatus(boolean isActive, boolean isListening) {
        mAiActive = isActive;
        mVoiceListening = isListening;
        
        // Update breathing animation
        if (mAssistantBreathing != null) {
            mAssistantBreathing.setVisibility(isActive ? View.VISIBLE : View.GONE);
        }
        
        // Update voice indicator
        if (mAssistantVoiceIndicator != null) {
            mAssistantVoiceIndicator.setVisibility(isListening ? View.VISIBLE : View.GONE);
        }
        
        // Update assistant icon color
        if (mAssistantIcon != null) {
            int colorRes = isActive ? R.color.jarvis_ai_active : 
                          (mCurrentSelectedIndex == 1 ? R.color.jarvis_nav_selected : R.color.jarvis_nav_unselected);
            mAssistantIcon.setColorFilter(mContext.getColor(colorRes));
        }
    }
    
    public void updateNotificationBadges(int count, boolean hasHighPriority) {
        // Update notification count
        if (mNotificationsCount != null) {
            if (count > 0) {
                mNotificationsCount.setText(String.valueOf(count));
                mNotificationsCount.setVisibility(View.VISIBLE);
            } else {
                mNotificationsCount.setVisibility(View.GONE);
            }
        }
        
        // Update priority indicator
        if (mNotificationsPriority != null) {
            mNotificationsPriority.setVisibility(hasHighPriority ? View.VISIBLE : View.GONE);
        }
    }
    
    public void updateMenuBadge(int count) {
        if (mMenuBadge != null) {
            if (count > 0) {
                mMenuBadge.setText(String.valueOf(count));
                mMenuBadge.setVisibility(View.VISIBLE);
            } else {
                mMenuBadge.setVisibility(View.GONE);
            }
        }
    }
    
    public void setCurrentSelection(int index) {
        if (index >= 0 && index < 5) {
            String[] itemIds = {"home", "assistant", "menu", "notifications", "profile"};
            selectNavigationItem(index, itemIds[index]);
        }
    }
    
    public int getCurrentSelection() {
        return mCurrentSelectedIndex;
    }
    
    public void destroy() {
        if (DEBUG) Log.d(TAG, "JarvisNavigationController destroyed");
    }
}
